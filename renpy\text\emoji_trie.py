# coding: utf-8
#
# This file is generated by module/emoji/make_emoji_trie.py. Do not
# edit it directly.
#
# Emoji trie data.
#
#
# It contains a data structure that can be used to determine if a
# sequence of codepoints is an emoji and if it should be rendered
# in an emoji font.
#
# Each trie node is a dictionary mapping codepoints to other dictionaries.
# If a codepoint is not present in the dictionarly, look up the '' key
# in the dictionary to find what it maps to. (It will be one of the constants
# below.
#
# The top-level dictionary is named emoji.

# The codepoint is not an emoji.
NOT_EMOJI = 0

# The codepoints are an emoji, but it may or may not be rendered in an emoji font.
UNQUALIFIED = 1

# The codepoints are part of a larger emoji sequence, and should be rendered
# in an emoji font when standing alone.
COMPONENT = 2

# The codepoints are an emoji, and should be rendered in an emoji font, but
# aren't fully qualified.
MINIMALLY_QUALIFIED = 3

# The codepoints are an emoji, and should be rendered in an emoji font.
QUALIFIED = 4


e1 = { '': 4, }
e203 = { '': 2, }
e16 = { '': 1, }
e57 = { '': 3, }
e4 = { '': 3, '️': e1, }
e5 = { '': 1, '️': e1, }
e9 = { '': 0, '💨': e1, }
e11 = { '': 0, '💫': e1, }
e14 = { '': 0, '🔥': e1, '🩹': e1, }
e22 = { '': 4, '🏻': e1, '🏼': e1, '🏽': e1, '🏾': e1, '🏿': e1, }
e23 = { '': 1, '️': e1, '🏻': e1, '🏼': e1, '🏽': e1, '🏾': e1, '🏿': e1, }
e27 = { '': 0, '🏼': e1, '🏽': e1, '🏾': e1, '🏿': e1, }
e30 = { '': 0, '🏻': e1, '🏽': e1, '🏾': e1, '🏿': e1, }
e33 = { '': 0, '🏻': e1, '🏼': e1, '🏾': e1, '🏿': e1, }
e36 = { '': 0, '🏻': e1, '🏼': e1, '🏽': e1, '🏿': e1, }
e39 = { '': 0, '🏻': e1, '🏼': e1, '🏽': e1, '🏾': e1, }
e47 = { '': 0, '🏻': e1, '🏼': e1, '🏽': e1, '🏾': e1, '🏿': e1, }
e104 = { '': 0, '🧑': e1, }
e108 = { '': 0, '🧒': e1, }
e147 = { '': 0, '👨': e1, }
e154 = { '': 0, '👦': e1, }
e156 = { '': 0, '👦': e1, '👧': e1, }
e191 = { '': 0, '👨': e1, '👩': e1, }
e205 = { '': 0, '🦺': e1, }
e207 = { '': 0, '⬛': e1, }
e211 = { '': 0, '⬛': e1, '🔥': e1, }
e213 = { '': 0, '🟫': e1, }
e215 = { '': 0, '🟩': e1, }
e218 = { '': 0, '💥': e1, }
e221 = { '': 0, '⃣': e1, }
e228 = { '': 0, '󠁿': e1, }
e237 = { '': 0, '🇨': e1, '🇩': e1, '🇪': e1, '🇫': e1, '🇬': e1, '🇮': e1, '🇱': e1, '🇲': e1, '🇴': e1, '🇶': e1, '🇷': e1, '🇸': e1, '🇹': e1, '🇺': e1, '🇼': e1, '🇽': e1, '🇿': e1, }
e238 = { '': 0, '🇦': e1, '🇧': e1, '🇩': e1, '🇪': e1, '🇫': e1, '🇬': e1, '🇭': e1, '🇮': e1, '🇯': e1, '🇱': e1, '🇲': e1, '🇳': e1, '🇴': e1, '🇶': e1, '🇷': e1, '🇸': e1, '🇹': e1, '🇻': e1, '🇼': e1, '🇾': e1, '🇿': e1, }
e239 = { '': 0, '🇦': e1, '🇨': e1, '🇩': e1, '🇫': e1, '🇬': e1, '🇭': e1, '🇮': e1, '🇰': e1, '🇱': e1, '🇲': e1, '🇳': e1, '🇴': e1, '🇵': e1, '🇷': e1, '🇺': e1, '🇻': e1, '🇼': e1, '🇽': e1, '🇾': e1, '🇿': e1, }
e240 = { '': 0, '🇪': e1, '🇬': e1, '🇯': e1, '🇰': e1, '🇲': e1, '🇴': e1, '🇿': e1, }
e241 = { '': 0, '🇦': e1, '🇨': e1, '🇪': e1, '🇬': e1, '🇭': e1, '🇷': e1, '🇸': e1, '🇹': e1, '🇺': e1, }
e242 = { '': 0, '🇮': e1, '🇯': e1, '🇰': e1, '🇲': e1, '🇴': e1, '🇷': e1, }
e243 = { '': 0, '🇦': e1, '🇧': e1, '🇩': e1, '🇪': e1, '🇫': e1, '🇬': e1, '🇭': e1, '🇮': e1, '🇱': e1, '🇲': e1, '🇳': e1, '🇵': e1, '🇶': e1, '🇷': e1, '🇸': e1, '🇹': e1, '🇺': e1, '🇼': e1, '🇾': e1, }
e244 = { '': 0, '🇰': e1, '🇲': e1, '🇳': e1, '🇷': e1, '🇹': e1, '🇺': e1, }
e245 = { '': 0, '🇨': e1, '🇩': e1, '🇪': e1, '🇱': e1, '🇲': e1, '🇳': e1, '🇴': e1, '🇶': e1, '🇷': e1, '🇸': e1, '🇹': e1, }
e246 = { '': 0, '🇪': e1, '🇲': e1, '🇴': e1, '🇵': e1, }
e247 = { '': 0, '🇪': e1, '🇬': e1, '🇭': e1, '🇮': e1, '🇲': e1, '🇳': e1, '🇵': e1, '🇷': e1, '🇼': e1, '🇾': e1, '🇿': e1, }
e248 = { '': 0, '🇦': e1, '🇧': e1, '🇨': e1, '🇮': e1, '🇰': e1, '🇷': e1, '🇸': e1, '🇹': e1, '🇺': e1, '🇻': e1, '🇾': e1, }
e249 = { '': 0, '🇦': e1, '🇨': e1, '🇩': e1, '🇪': e1, '🇫': e1, '🇬': e1, '🇭': e1, '🇰': e1, '🇱': e1, '🇲': e1, '🇳': e1, '🇴': e1, '🇵': e1, '🇶': e1, '🇷': e1, '🇸': e1, '🇹': e1, '🇺': e1, '🇻': e1, '🇼': e1, '🇽': e1, '🇾': e1, '🇿': e1, }
e250 = { '': 0, '🇦': e1, '🇨': e1, '🇪': e1, '🇫': e1, '🇬': e1, '🇮': e1, '🇱': e1, '🇴': e1, '🇵': e1, '🇷': e1, '🇺': e1, '🇿': e1, }
e251 = { '': 0, '🇲': e1, }
e252 = { '': 0, '🇦': e1, '🇪': e1, '🇫': e1, '🇬': e1, '🇭': e1, '🇰': e1, '🇱': e1, '🇲': e1, '🇳': e1, '🇷': e1, '🇸': e1, '🇹': e1, '🇼': e1, '🇾': e1, }
e253 = { '': 0, '🇦': e1, }
e254 = { '': 0, '🇪': e1, '🇴': e1, '🇸': e1, '🇺': e1, '🇼': e1, }
e255 = { '': 0, '🇦': e1, '🇧': e1, '🇨': e1, '🇩': e1, '🇪': e1, '🇬': e1, '🇭': e1, '🇮': e1, '🇯': e1, '🇰': e1, '🇱': e1, '🇲': e1, '🇳': e1, '🇴': e1, '🇷': e1, '🇸': e1, '🇹': e1, '🇻': e1, '🇽': e1, '🇾': e1, '🇿': e1, }
e256 = { '': 0, '🇦': e1, '🇨': e1, '🇩': e1, '🇫': e1, '🇬': e1, '🇭': e1, '🇯': e1, '🇰': e1, '🇱': e1, '🇲': e1, '🇳': e1, '🇴': e1, '🇷': e1, '🇹': e1, '🇻': e1, '🇼': e1, '🇿': e1, }
e257 = { '': 0, '🇦': e1, '🇬': e1, '🇲': e1, '🇳': e1, '🇸': e1, '🇾': e1, '🇿': e1, }
e258 = { '': 0, '🇦': e1, '🇨': e1, '🇪': e1, '🇬': e1, '🇮': e1, '🇳': e1, '🇺': e1, }
e259 = { '': 0, '🇫': e1, '🇸': e1, }
e260 = { '': 0, '🇰': e1, }
e261 = { '': 0, '🇪': e1, '🇹': e1, }
e262 = { '': 0, '🇦': e1, '🇲': e1, '🇼': e1, }
e15 = { '': 0, '🔥': e16, '🩹': e16, }
e21 = { '': 1, '️': e16, }
e219 = { '': 0, '💥': e16, }
e56 = { '': 0, '🏼': e57, '🏽': e57, '🏾': e57, '🏿': e57, }
e68 = { '': 0, '🏻': e57, '🏽': e57, '🏾': e57, '🏿': e57, }
e79 = { '': 0, '🏻': e57, '🏼': e57, '🏾': e57, '🏿': e57, }
e90 = { '': 0, '🏻': e57, '🏼': e57, '🏽': e57, '🏿': e57, }
e101 = { '': 0, '🏻': e57, '🏼': e57, '🏽': e57, '🏾': e57, }
e125 = { '': 0, '🏻': e57, '🏼': e57, '🏽': e57, '🏾': e57, '🏿': e57, }
e150 = { '': 0, '👨': e57, }
e194 = { '': 0, '👨': e57, '👩': e57, }
e202 = { '': 3, '️': e57, }
e3 = { '': 0, '↔': e4, '↕': e4, }
e7 = { '': 0, '🌫': e4, }
e19 = { '': 0, '🗨': e4, }
e44 = { '': 0, '➡': e4, }
e111 = { '': 0, '♀': e4, '♂': e4, }
e209 = { '': 0, '❄': e4, }
e223 = { '': 0, '☠': e4, }
e235 = { '': 0, '⚧': e4, '🌈': e1, }
e8 = { '': 4, '‍': e9, }
e10 = { '': 4, '‍': e11, }
e13 = { '': 4, '‍': e14, }
e26 = { '': 0, '🫲': e27, }
e52 = { '': 0, '🧑': e27, }
e116 = { '': 0, '👨': e27, }
e161 = { '': 0, '👨': e27, '👩': e27, }
e29 = { '': 0, '🫲': e30, }
e64 = { '': 0, '🧑': e30, }
e129 = { '': 0, '👨': e30, }
e173 = { '': 0, '👨': e30, '👩': e30, }
e32 = { '': 0, '🫲': e33, }
e75 = { '': 0, '🧑': e33, }
e133 = { '': 0, '👨': e33, }
e177 = { '': 0, '👨': e33, '👩': e33, }
e35 = { '': 0, '🫲': e36, }
e86 = { '': 0, '🧑': e36, }
e137 = { '': 0, '👨': e36, }
e181 = { '': 0, '👨': e36, '👩': e36, }
e38 = { '': 0, '🫲': e39, }
e97 = { '': 0, '🧑': e39, }
e141 = { '': 0, '👨': e39, }
e185 = { '': 0, '👨': e39, '👩': e39, }
e46 = { '': 0, '🧑': e47, }
e121 = { '': 0, '👨': e47, }
e166 = { '': 0, '👨': e47, '👩': e47, }
e103 = { '': 0, '‍': e104, }
e107 = { '': 4, '‍': e108, }
e146 = { '': 0, '‍': e147, }
e153 = { '': 4, '‍': e154, }
e155 = { '': 4, '‍': e156, }
e190 = { '': 0, '‍': e191, }
e204 = { '': 4, '‍': e205, }
e206 = { '': 4, '‍': e207, }
e210 = { '': 4, '‍': e211, }
e212 = { '': 4, '‍': e213, }
e214 = { '': 4, '‍': e215, }
e217 = { '': 4, '‍': e218, }
e220 = { '': 0, '⃣': e16, '️': e221, }
e227 = { '': 0, '󠁧': e228, }
e230 = { '': 0, '󠁴': e228, }
e232 = { '': 0, '󠁳': e228, }
e20 = { '': 0, '🗨': e21, }
e196 = { '': 0, '♀': e21, '♂': e21, }
e236 = { '': 0, '⚧': e21, '🌈': e16, }
e55 = { '': 0, '🧑': e56, }
e67 = { '': 0, '🧑': e68, }
e78 = { '': 0, '🧑': e79, }
e89 = { '': 0, '🧑': e90, }
e100 = { '': 0, '🧑': e101, }
e124 = { '': 0, '👨': e125, }
e169 = { '': 0, '👨': e125, '👩': e125, }
e149 = { '': 0, '‍': e150, }
e193 = { '': 0, '‍': e194, }
e201 = { '': 0, '➡': e202, }
e2 = { '': 4, '‍': e3, }
e6 = { '': 4, '‍': e7, }
e18 = { '': 4, '‍': e19, }
e43 = { '': 4, '‍': e44, }
e110 = { '': 4, '‍': e111, }
e208 = { '': 4, '‍': e209, }
e234 = { '': 4, '‍': e235, }
e12 = { '': 1, '‍': e15, '️': e13, }
e25 = { '': 4, '‍': e26, }
e51 = { '': 0, '‍': e52, }
e115 = { '': 0, '‍': e116, }
e160 = { '': 0, '‍': e161, }
e28 = { '': 4, '‍': e29, }
e63 = { '': 0, '‍': e64, }
e128 = { '': 0, '‍': e129, }
e172 = { '': 0, '‍': e173, }
e31 = { '': 4, '‍': e32, }
e74 = { '': 0, '‍': e75, }
e132 = { '': 0, '‍': e133, }
e176 = { '': 0, '‍': e177, }
e34 = { '': 4, '‍': e35, }
e85 = { '': 0, '‍': e86, }
e136 = { '': 0, '‍': e137, }
e180 = { '': 0, '‍': e181, }
e37 = { '': 4, '‍': e38, }
e96 = { '': 0, '‍': e97, }
e140 = { '': 0, '‍': e141, }
e184 = { '': 0, '‍': e185, }
e45 = { '': 0, '‍': e46, }
e120 = { '': 0, '‍': e121, }
e165 = { '': 0, '‍': e166, }
e106 = { '': 0, '🧒': e107, }
e145 = { '': 0, '👨': e1, '💋': e146, }
e152 = { '': 0, '👦': e153, '👧': e155, }
e189 = { '': 0, '👨': e1, '👩': e1, '💋': e190, }
e216 = { '': 1, '‍': e219, '️': e217, }
e226 = { '': 0, '󠁮': e227, }
e229 = { '': 0, '󠁣': e230, }
e231 = { '': 0, '󠁬': e232, }
e54 = { '': 0, '‍': e55, }
e66 = { '': 0, '‍': e67, }
e77 = { '': 0, '‍': e78, }
e88 = { '': 0, '‍': e89, }
e99 = { '': 0, '‍': e100, }
e123 = { '': 0, '‍': e124, }
e168 = { '': 0, '‍': e169, }
e148 = { '': 0, '👨': e57, '💋': e149, }
e192 = { '': 0, '👨': e57, '👩': e57, '💋': e193, }
e17 = { '': 1, '‍': e20, '️': e18, }
e200 = { '': 3, '‍': e201, '️': e43, }
e109 = { '': 4, '‍': e111, '🏻': e110, '🏼': e110, '🏽': e110, '🏾': e110, '🏿': e110, }
e195 = { '': 1, '‍': e196, '️': e110, '🏻': e110, '🏼': e110, '🏽': e110, '🏾': e110, '🏿': e110, }
e233 = { '': 1, '‍': e236, '️': e234, }
e50 = { '': 0, '💋': e51, '🧑': e27, }
e62 = { '': 0, '💋': e63, '🧑': e30, }
e73 = { '': 0, '💋': e74, '🧑': e33, }
e84 = { '': 0, '💋': e85, '🧑': e36, }
e24 = { '': 4, '🏻': e25, '🏼': e28, '🏽': e31, '🏾': e34, '🏿': e37, }
e95 = { '': 0, '💋': e96, '🧑': e39, }
e119 = { '': 0, '👨': e47, '💋': e120, }
e164 = { '': 0, '👨': e47, '👩': e47, '💋': e165, }
e105 = { '': 0, '‍': e106, }
e144 = { '': 0, '‍': e145, }
e151 = { '': 0, '‍': e152, }
e188 = { '': 0, '‍': e189, }
e225 = { '': 0, '󠁥': e226, '󠁳': e229, '󠁷': e231, }
e53 = { '': 0, '💋': e54, '🧑': e56, }
e65 = { '': 0, '💋': e66, '🧑': e68, }
e76 = { '': 0, '💋': e77, '🧑': e79, }
e87 = { '': 0, '💋': e88, '🧑': e90, }
e98 = { '': 0, '💋': e99, '🧑': e101, }
e122 = { '': 0, '👨': e125, '💋': e123, }
e167 = { '': 0, '👨': e125, '👩': e125, '💋': e168, }
e199 = { '': 0, '♀': e200, '♂': e200, '➡': e4, }
e49 = { '': 0, '‍': e50, }
e61 = { '': 0, '‍': e62, }
e72 = { '': 0, '‍': e73, }
e83 = { '': 0, '‍': e84, }
e94 = { '': 0, '‍': e95, }
e118 = { '': 0, '‍': e119, }
e163 = { '': 0, '‍': e164, }
e102 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '🌾': e1, '🍳': e1, '🍼': e1, '🎄': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e103, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, '🧑': e105, '🧒': e107, }
e143 = { '': 0, '‍': e148, '️': e144, }
e187 = { '': 0, '‍': e192, '️': e188, }
e224 = { '': 0, '󠁢': e225, }
e198 = { '': 4, '‍': e199, }
e48 = { '': 0, '‍': e53, '️': e49, }
e60 = { '': 0, '‍': e65, '️': e61, }
e71 = { '': 0, '‍': e76, '️': e72, }
e82 = { '': 0, '‍': e87, '️': e83, }
e93 = { '': 0, '‍': e98, '️': e94, }
e117 = { '': 0, '‍': e122, '️': e118, }
e162 = { '': 0, '‍': e167, '️': e163, }
e142 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e143, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '👦': e153, '👧': e155, '👨': e151, '👩': e151, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e186 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e187, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '👦': e153, '👧': e155, '👩': e151, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e222 = { '': 4, '‍': e223, '󠁧': e224, }
e197 = { '': 4, '‍': e199, '🏻': e198, '🏼': e198, '🏽': e198, '🏾': e198, '🏿': e198, }
e42 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e48, '🌾': e1, '🍳': e1, '🍼': e1, '🎄': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e45, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e59 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e60, '🌾': e1, '🍳': e1, '🍼': e1, '🎄': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e45, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e70 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e71, '🌾': e1, '🍳': e1, '🍼': e1, '🎄': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e45, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e81 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e82, '🌾': e1, '🍳': e1, '🍼': e1, '🎄': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e45, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e92 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e93, '🌾': e1, '🍳': e1, '🍼': e1, '🎄': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e45, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e114 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e117, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e115, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e127 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e117, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e128, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e131 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e117, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e132, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e135 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e117, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e136, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e139 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e117, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e140, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e159 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e162, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e160, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e171 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e162, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e172, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e175 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e162, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e176, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e179 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e162, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e180, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e183 = { '': 0, '⚕': e4, '⚖': e4, '✈': e4, '❤': e162, '🌾': e1, '🍳': e1, '🍼': e1, '🎓': e1, '🎤': e1, '🎨': e1, '🏫': e1, '🏭': e1, '💻': e1, '💼': e1, '🔧': e1, '🔬': e1, '🚀': e1, '🚒': e1, '🤝': e184, '🦯': e43, '🦰': e1, '🦱': e1, '🦲': e1, '🦳': e1, '🦼': e43, '🦽': e43, }
e41 = { '': 4, '‍': e42, }
e58 = { '': 4, '‍': e59, }
e69 = { '': 4, '‍': e70, }
e80 = { '': 4, '‍': e81, }
e91 = { '': 4, '‍': e92, }
e113 = { '': 4, '‍': e114, }
e126 = { '': 4, '‍': e127, }
e130 = { '': 4, '‍': e131, }
e134 = { '': 4, '‍': e135, }
e138 = { '': 4, '‍': e139, }
e158 = { '': 4, '‍': e159, }
e170 = { '': 4, '‍': e171, }
e174 = { '': 4, '‍': e175, }
e178 = { '': 4, '‍': e179, }
e182 = { '': 4, '‍': e183, }
e40 = { '': 4, '‍': e102, '🏻': e41, '🏼': e58, '🏽': e69, '🏾': e80, '🏿': e91, }
e112 = { '': 4, '‍': e142, '🏻': e113, '🏼': e126, '🏽': e130, '🏾': e134, '🏿': e138, }
e157 = { '': 4, '‍': e186, '🏻': e158, '🏼': e170, '🏽': e174, '🏾': e178, '🏿': e182, }
emoji = { '': 0, '#': e220, '*': e220, '0': e220, '1': e220, '2': e220, '3': e220, '4': e220, '5': e220, '6': e220, '7': e220, '8': e220, '9': e220, '©': e5, '®': e5, '‼': e5, '⁉': e5, '™': e5, 'ℹ': e5, '↔': e5, '↕': e5, '↖': e5, '↗': e5, '↘': e5, '↙': e5, '↩': e5, '↪': e5, '⌚': e1, '⌛': e1, '⌨': e5, '⏏': e5, '⏩': e1, '⏪': e1, '⏫': e1, '⏬': e1, '⏭': e5, '⏮': e5, '⏯': e5, '⏰': e1, '⏱': e5, '⏲': e5, '⏳': e1, '⏸': e5, '⏹': e5, '⏺': e5, 'Ⓜ': e5, '▪': e5, '▫': e5, '▶': e5, '◀': e5, '◻': e5, '◼': e5, '◽': e1, '◾': e1, '☀': e5, '☁': e5, '☂': e5, '☃': e5, '☄': e5, '☎': e5, '☑': e5, '☔': e1, '☕': e1, '☘': e5, '☝': e23, '☠': e5, '☢': e5, '☣': e5, '☦': e5, '☪': e5, '☮': e5, '☯': e5, '☸': e5, '☹': e5, '☺': e5, '♀': e5, '♂': e5, '♈': e1, '♉': e1, '♊': e1, '♋': e1, '♌': e1, '♍': e1, '♎': e1, '♏': e1, '♐': e1, '♑': e1, '♒': e1, '♓': e1, '♟': e5, '♠': e5, '♣': e5, '♥': e5, '♦': e5, '♨': e5, '♻': e5, '♾': e5, '♿': e1, '⚒': e5, '⚓': e1, '⚔': e5, '⚕': e5, '⚖': e5, '⚗': e5, '⚙': e5, '⚛': e5, '⚜': e5, '⚠': e5, '⚡': e1, '⚧': e5, '⚪': e1, '⚫': e1, '⚰': e5, '⚱': e5, '⚽': e1, '⚾': e1, '⛄': e1, '⛅': e1, '⛈': e5, '⛎': e1, '⛏': e5, '⛑': e5, '⛓': e216, '⛔': e1, '⛩': e5, '⛪': e1, '⛰': e5, '⛱': e5, '⛲': e1, '⛳': e1, '⛴': e5, '⛵': e1, '⛷': e5, '⛸': e5, '⛹': e195, '⛺': e1, '⛽': e1, '✂': e5, '✅': e1, '✈': e5, '✉': e5, '✊': e22, '✋': e22, '✌': e23, '✍': e23, '✏': e5, '✒': e5, '✔': e5, '✖': e5, '✝': e5, '✡': e5, '✨': e1, '✳': e5, '✴': e5, '❄': e5, '❇': e5, '❌': e1, '❎': e1, '❓': e1, '❔': e1, '❕': e1, '❗': e1, '❣': e5, '❤': e12, '➕': e1, '➖': e1, '➗': e1, '➡': e5, '➰': e1, '➿': e1, '⤴': e5, '⤵': e5, '⬅': e5, '⬆': e5, '⬇': e5, '⬛': e1, '⬜': e1, '⭐': e1, '⭕': e1, '〰': e5, '〽': e5, '㊗': e5, '㊙': e5, '🀄': e1, '🃏': e1, '🅰': e5, '🅱': e5, '🅾': e5, '🅿': e5, '🆎': e1, '🆑': e1, '🆒': e1, '🆓': e1, '🆔': e1, '🆕': e1, '🆖': e1, '🆗': e1, '🆘': e1, '🆙': e1, '🆚': e1, '🇦': e237, '🇧': e238, '🇨': e239, '🇩': e240, '🇪': e241, '🇫': e242, '🇬': e243, '🇭': e244, '🇮': e245, '🇯': e246, '🇰': e247, '🇱': e248, '🇲': e249, '🇳': e250, '🇴': e251, '🇵': e252, '🇶': e253, '🇷': e254, '🇸': e255, '🇹': e256, '🇺': e257, '🇻': e258, '🇼': e259, '🇽': e260, '🇾': e261, '🇿': e262, '🈁': e1, '🈂': e5, '🈚': e1, '🈯': e1, '🈲': e1, '🈳': e1, '🈴': e1, '🈵': e1, '🈶': e1, '🈷': e5, '🈸': e1, '🈹': e1, '🈺': e1, '🉐': e1, '🉑': e1, '🌀': e1, '🌁': e1, '🌂': e1, '🌃': e1, '🌄': e1, '🌅': e1, '🌆': e1, '🌇': e1, '🌈': e1, '🌉': e1, '🌊': e1, '🌋': e1, '🌌': e1, '🌍': e1, '🌎': e1, '🌏': e1, '🌐': e1, '🌑': e1, '🌒': e1, '🌓': e1, '🌔': e1, '🌕': e1, '🌖': e1, '🌗': e1, '🌘': e1, '🌙': e1, '🌚': e1, '🌛': e1, '🌜': e1, '🌝': e1, '🌞': e1, '🌟': e1, '🌠': e1, '🌡': e5, '🌤': e5, '🌥': e5, '🌦': e5, '🌧': e5, '🌨': e5, '🌩': e5, '🌪': e5, '🌫': e5, '🌬': e5, '🌭': e1, '🌮': e1, '🌯': e1, '🌰': e1, '🌱': e1, '🌲': e1, '🌳': e1, '🌴': e1, '🌵': e1, '🌶': e5, '🌷': e1, '🌸': e1, '🌹': e1, '🌺': e1, '🌻': e1, '🌼': e1, '🌽': e1, '🌾': e1, '🌿': e1, '🍀': e1, '🍁': e1, '🍂': e1, '🍃': e1, '🍄': e212, '🍅': e1, '🍆': e1, '🍇': e1, '🍈': e1, '🍉': e1, '🍊': e1, '🍋': e214, '🍌': e1, '🍍': e1, '🍎': e1, '🍏': e1, '🍐': e1, '🍑': e1, '🍒': e1, '🍓': e1, '🍔': e1, '🍕': e1, '🍖': e1, '🍗': e1, '🍘': e1, '🍙': e1, '🍚': e1, '🍛': e1, '🍜': e1, '🍝': e1, '🍞': e1, '🍟': e1, '🍠': e1, '🍡': e1, '🍢': e1, '🍣': e1, '🍤': e1, '🍥': e1, '🍦': e1, '🍧': e1, '🍨': e1, '🍩': e1, '🍪': e1, '🍫': e1, '🍬': e1, '🍭': e1, '🍮': e1, '🍯': e1, '🍰': e1, '🍱': e1, '🍲': e1, '🍳': e1, '🍴': e1, '🍵': e1, '🍶': e1, '🍷': e1, '🍸': e1, '🍹': e1, '🍺': e1, '🍻': e1, '🍼': e1, '🍽': e5, '🍾': e1, '🍿': e1, '🎀': e1, '🎁': e1, '🎂': e1, '🎃': e1, '🎄': e1, '🎅': e22, '🎆': e1, '🎇': e1, '🎈': e1, '🎉': e1, '🎊': e1, '🎋': e1, '🎌': e1, '🎍': e1, '🎎': e1, '🎏': e1, '🎐': e1, '🎑': e1, '🎒': e1, '🎓': e1, '🎖': e5, '🎗': e5, '🎙': e5, '🎚': e5, '🎛': e5, '🎞': e5, '🎟': e5, '🎠': e1, '🎡': e1, '🎢': e1, '🎣': e1, '🎤': e1, '🎥': e1, '🎦': e1, '🎧': e1, '🎨': e1, '🎩': e1, '🎪': e1, '🎫': e1, '🎬': e1, '🎭': e1, '🎮': e1, '🎯': e1, '🎰': e1, '🎱': e1, '🎲': e1, '🎳': e1, '🎴': e1, '🎵': e1, '🎶': e1, '🎷': e1, '🎸': e1, '🎹': e1, '🎺': e1, '🎻': e1, '🎼': e1, '🎽': e1, '🎾': e1, '🎿': e1, '🏀': e1, '🏁': e1, '🏂': e22, '🏃': e197, '🏄': e109, '🏅': e1, '🏆': e1, '🏇': e22, '🏈': e1, '🏉': e1, '🏊': e109, '🏋': e195, '🏌': e195, '🏍': e5, '🏎': e5, '🏏': e1, '🏐': e1, '🏑': e1, '🏒': e1, '🏓': e1, '🏔': e5, '🏕': e5, '🏖': e5, '🏗': e5, '🏘': e5, '🏙': e5, '🏚': e5, '🏛': e5, '🏜': e5, '🏝': e5, '🏞': e5, '🏟': e5, '🏠': e1, '🏡': e1, '🏢': e1, '🏣': e1, '🏤': e1, '🏥': e1, '🏦': e1, '🏧': e1, '🏨': e1, '🏩': e1, '🏪': e1, '🏫': e1, '🏬': e1, '🏭': e1, '🏮': e1, '🏯': e1, '🏰': e1, '🏳': e233, '🏴': e222, '🏵': e5, '🏷': e5, '🏸': e1, '🏹': e1, '🏺': e1, '🏻': e203, '🏼': e203, '🏽': e203, '🏾': e203, '🏿': e203, '🐀': e1, '🐁': e1, '🐂': e1, '🐃': e1, '🐄': e1, '🐅': e1, '🐆': e1, '🐇': e1, '🐈': e206, '🐉': e1, '🐊': e1, '🐋': e1, '🐌': e1, '🐍': e1, '🐎': e1, '🐏': e1, '🐐': e1, '🐑': e1, '🐒': e1, '🐓': e1, '🐔': e1, '🐕': e204, '🐖': e1, '🐗': e1, '🐘': e1, '🐙': e1, '🐚': e1, '🐛': e1, '🐜': e1, '🐝': e1, '🐞': e1, '🐟': e1, '🐠': e1, '🐡': e1, '🐢': e1, '🐣': e1, '🐤': e1, '🐥': e1, '🐦': e210, '🐧': e1, '🐨': e1, '🐩': e1, '🐪': e1, '🐫': e1, '🐬': e1, '🐭': e1, '🐮': e1, '🐯': e1, '🐰': e1, '🐱': e1, '🐲': e1, '🐳': e1, '🐴': e1, '🐵': e1, '🐶': e1, '🐷': e1, '🐸': e1, '🐹': e1, '🐺': e1, '🐻': e208, '🐼': e1, '🐽': e1, '🐾': e1, '🐿': e5, '👀': e1, '👁': e17, '👂': e22, '👃': e22, '👄': e1, '👅': e1, '👆': e22, '👇': e22, '👈': e22, '👉': e22, '👊': e22, '👋': e22, '👌': e22, '👍': e22, '👎': e22, '👏': e22, '👐': e22, '👑': e1, '👒': e1, '👓': e1, '👔': e1, '👕': e1, '👖': e1, '👗': e1, '👘': e1, '👙': e1, '👚': e1, '👛': e1, '👜': e1, '👝': e1, '👞': e1, '👟': e1, '👠': e1, '👡': e1, '👢': e1, '👣': e1, '👤': e1, '👥': e1, '👦': e22, '👧': e22, '👨': e112, '👩': e157, '👪': e1, '👫': e22, '👬': e22, '👭': e22, '👮': e109, '👯': e110, '👰': e109, '👱': e109, '👲': e22, '👳': e109, '👴': e22, '👵': e22, '👶': e22, '👷': e109, '👸': e22, '👹': e1, '👺': e1, '👻': e1, '👼': e22, '👽': e1, '👾': e1, '👿': e1, '💀': e1, '💁': e109, '💂': e109, '💃': e22, '💄': e1, '💅': e22, '💆': e109, '💇': e109, '💈': e1, '💉': e1, '💊': e1, '💋': e1, '💌': e1, '💍': e1, '💎': e1, '💏': e22, '💐': e1, '💑': e22, '💒': e1, '💓': e1, '💔': e1, '💕': e1, '💖': e1, '💗': e1, '💘': e1, '💙': e1, '💚': e1, '💛': e1, '💜': e1, '💝': e1, '💞': e1, '💟': e1, '💠': e1, '💡': e1, '💢': e1, '💣': e1, '💤': e1, '💥': e1, '💦': e1, '💧': e1, '💨': e1, '💩': e1, '💪': e22, '💫': e1, '💬': e1, '💭': e1, '💮': e1, '💯': e1, '💰': e1, '💱': e1, '💲': e1, '💳': e1, '💴': e1, '💵': e1, '💶': e1, '💷': e1, '💸': e1, '💹': e1, '💺': e1, '💻': e1, '💼': e1, '💽': e1, '💾': e1, '💿': e1, '📀': e1, '📁': e1, '📂': e1, '📃': e1, '📄': e1, '📅': e1, '📆': e1, '📇': e1, '📈': e1, '📉': e1, '📊': e1, '📋': e1, '📌': e1, '📍': e1, '📎': e1, '📏': e1, '📐': e1, '📑': e1, '📒': e1, '📓': e1, '📔': e1, '📕': e1, '📖': e1, '📗': e1, '📘': e1, '📙': e1, '📚': e1, '📛': e1, '📜': e1, '📝': e1, '📞': e1, '📟': e1, '📠': e1, '📡': e1, '📢': e1, '📣': e1, '📤': e1, '📥': e1, '📦': e1, '📧': e1, '📨': e1, '📩': e1, '📪': e1, '📫': e1, '📬': e1, '📭': e1, '📮': e1, '📯': e1, '📰': e1, '📱': e1, '📲': e1, '📳': e1, '📴': e1, '📵': e1, '📶': e1, '📷': e1, '📸': e1, '📹': e1, '📺': e1, '📻': e1, '📼': e1, '📽': e5, '📿': e1, '🔀': e1, '🔁': e1, '🔂': e1, '🔃': e1, '🔄': e1, '🔅': e1, '🔆': e1, '🔇': e1, '🔈': e1, '🔉': e1, '🔊': e1, '🔋': e1, '🔌': e1, '🔍': e1, '🔎': e1, '🔏': e1, '🔐': e1, '🔑': e1, '🔒': e1, '🔓': e1, '🔔': e1, '🔕': e1, '🔖': e1, '🔗': e1, '🔘': e1, '🔙': e1, '🔚': e1, '🔛': e1, '🔜': e1, '🔝': e1, '🔞': e1, '🔟': e1, '🔠': e1, '🔡': e1, '🔢': e1, '🔣': e1, '🔤': e1, '🔥': e1, '🔦': e1, '🔧': e1, '🔨': e1, '🔩': e1, '🔪': e1, '🔫': e1, '🔬': e1, '🔭': e1, '🔮': e1, '🔯': e1, '🔰': e1, '🔱': e1, '🔲': e1, '🔳': e1, '🔴': e1, '🔵': e1, '🔶': e1, '🔷': e1, '🔸': e1, '🔹': e1, '🔺': e1, '🔻': e1, '🔼': e1, '🔽': e1, '🕉': e5, '🕊': e5, '🕋': e1, '🕌': e1, '🕍': e1, '🕎': e1, '🕐': e1, '🕑': e1, '🕒': e1, '🕓': e1, '🕔': e1, '🕕': e1, '🕖': e1, '🕗': e1, '🕘': e1, '🕙': e1, '🕚': e1, '🕛': e1, '🕜': e1, '🕝': e1, '🕞': e1, '🕟': e1, '🕠': e1, '🕡': e1, '🕢': e1, '🕣': e1, '🕤': e1, '🕥': e1, '🕦': e1, '🕧': e1, '🕯': e5, '🕰': e5, '🕳': e5, '🕴': e23, '🕵': e195, '🕶': e5, '🕷': e5, '🕸': e5, '🕹': e5, '🕺': e22, '🖇': e5, '🖊': e5, '🖋': e5, '🖌': e5, '🖍': e5, '🖐': e23, '🖕': e22, '🖖': e22, '🖤': e1, '🖥': e5, '🖨': e5, '🖱': e5, '🖲': e5, '🖼': e5, '🗂': e5, '🗃': e5, '🗄': e5, '🗑': e5, '🗒': e5, '🗓': e5, '🗜': e5, '🗝': e5, '🗞': e5, '🗡': e5, '🗣': e5, '🗨': e5, '🗯': e5, '🗳': e5, '🗺': e5, '🗻': e1, '🗼': e1, '🗽': e1, '🗾': e1, '🗿': e1, '😀': e1, '😁': e1, '😂': e1, '😃': e1, '😄': e1, '😅': e1, '😆': e1, '😇': e1, '😈': e1, '😉': e1, '😊': e1, '😋': e1, '😌': e1, '😍': e1, '😎': e1, '😏': e1, '😐': e1, '😑': e1, '😒': e1, '😓': e1, '😔': e1, '😕': e1, '😖': e1, '😗': e1, '😘': e1, '😙': e1, '😚': e1, '😛': e1, '😜': e1, '😝': e1, '😞': e1, '😟': e1, '😠': e1, '😡': e1, '😢': e1, '😣': e1, '😤': e1, '😥': e1, '😦': e1, '😧': e1, '😨': e1, '😩': e1, '😪': e1, '😫': e1, '😬': e1, '😭': e1, '😮': e8, '😯': e1, '😰': e1, '😱': e1, '😲': e1, '😳': e1, '😴': e1, '😵': e10, '😶': e6, '😷': e1, '😸': e1, '😹': e1, '😺': e1, '😻': e1, '😼': e1, '😽': e1, '😾': e1, '😿': e1, '🙀': e1, '🙁': e1, '🙂': e2, '🙃': e1, '🙄': e1, '🙅': e109, '🙆': e109, '🙇': e109, '🙈': e1, '🙉': e1, '🙊': e1, '🙋': e109, '🙌': e22, '🙍': e109, '🙎': e109, '🙏': e22, '🚀': e1, '🚁': e1, '🚂': e1, '🚃': e1, '🚄': e1, '🚅': e1, '🚆': e1, '🚇': e1, '🚈': e1, '🚉': e1, '🚊': e1, '🚋': e1, '🚌': e1, '🚍': e1, '🚎': e1, '🚏': e1, '🚐': e1, '🚑': e1, '🚒': e1, '🚓': e1, '🚔': e1, '🚕': e1, '🚖': e1, '🚗': e1, '🚘': e1, '🚙': e1, '🚚': e1, '🚛': e1, '🚜': e1, '🚝': e1, '🚞': e1, '🚟': e1, '🚠': e1, '🚡': e1, '🚢': e1, '🚣': e109, '🚤': e1, '🚥': e1, '🚦': e1, '🚧': e1, '🚨': e1, '🚩': e1, '🚪': e1, '🚫': e1, '🚬': e1, '🚭': e1, '🚮': e1, '🚯': e1, '🚰': e1, '🚱': e1, '🚲': e1, '🚳': e1, '🚴': e109, '🚵': e109, '🚶': e197, '🚷': e1, '🚸': e1, '🚹': e1, '🚺': e1, '🚻': e1, '🚼': e1, '🚽': e1, '🚾': e1, '🚿': e1, '🛀': e22, '🛁': e1, '🛂': e1, '🛃': e1, '🛄': e1, '🛅': e1, '🛋': e5, '🛌': e22, '🛍': e5, '🛎': e5, '🛏': e5, '🛐': e1, '🛑': e1, '🛒': e1, '🛕': e1, '🛖': e1, '🛗': e1, '🛜': e1, '🛝': e1, '🛞': e1, '🛟': e1, '🛠': e5, '🛡': e5, '🛢': e5, '🛣': e5, '🛤': e5, '🛥': e5, '🛩': e5, '🛫': e1, '🛬': e1, '🛰': e5, '🛳': e5, '🛴': e1, '🛵': e1, '🛶': e1, '🛷': e1, '🛸': e1, '🛹': e1, '🛺': e1, '🛻': e1, '🛼': e1, '🟠': e1, '🟡': e1, '🟢': e1, '🟣': e1, '🟤': e1, '🟥': e1, '🟦': e1, '🟧': e1, '🟨': e1, '🟩': e1, '🟪': e1, '🟫': e1, '🟰': e1, '🤌': e22, '🤍': e1, '🤎': e1, '🤏': e22, '🤐': e1, '🤑': e1, '🤒': e1, '🤓': e1, '🤔': e1, '🤕': e1, '🤖': e1, '🤗': e1, '🤘': e22, '🤙': e22, '🤚': e22, '🤛': e22, '🤜': e22, '🤝': e22, '🤞': e22, '🤟': e22, '🤠': e1, '🤡': e1, '🤢': e1, '🤣': e1, '🤤': e1, '🤥': e1, '🤦': e109, '🤧': e1, '🤨': e1, '🤩': e1, '🤪': e1, '🤫': e1, '🤬': e1, '🤭': e1, '🤮': e1, '🤯': e1, '🤰': e22, '🤱': e22, '🤲': e22, '🤳': e22, '🤴': e22, '🤵': e109, '🤶': e22, '🤷': e109, '🤸': e109, '🤹': e109, '🤺': e1, '🤼': e110, '🤽': e109, '🤾': e109, '🤿': e1, '🥀': e1, '🥁': e1, '🥂': e1, '🥃': e1, '🥄': e1, '🥅': e1, '🥇': e1, '🥈': e1, '🥉': e1, '🥊': e1, '🥋': e1, '🥌': e1, '🥍': e1, '🥎': e1, '🥏': e1, '🥐': e1, '🥑': e1, '🥒': e1, '🥓': e1, '🥔': e1, '🥕': e1, '🥖': e1, '🥗': e1, '🥘': e1, '🥙': e1, '🥚': e1, '🥛': e1, '🥜': e1, '🥝': e1, '🥞': e1, '🥟': e1, '🥠': e1, '🥡': e1, '🥢': e1, '🥣': e1, '🥤': e1, '🥥': e1, '🥦': e1, '🥧': e1, '🥨': e1, '🥩': e1, '🥪': e1, '🥫': e1, '🥬': e1, '🥭': e1, '🥮': e1, '🥯': e1, '🥰': e1, '🥱': e1, '🥲': e1, '🥳': e1, '🥴': e1, '🥵': e1, '🥶': e1, '🥷': e22, '🥸': e1, '🥹': e1, '🥺': e1, '🥻': e1, '🥼': e1, '🥽': e1, '🥾': e1, '🥿': e1, '🦀': e1, '🦁': e1, '🦂': e1, '🦃': e1, '🦄': e1, '🦅': e1, '🦆': e1, '🦇': e1, '🦈': e1, '🦉': e1, '🦊': e1, '🦋': e1, '🦌': e1, '🦍': e1, '🦎': e1, '🦏': e1, '🦐': e1, '🦑': e1, '🦒': e1, '🦓': e1, '🦔': e1, '🦕': e1, '🦖': e1, '🦗': e1, '🦘': e1, '🦙': e1, '🦚': e1, '🦛': e1, '🦜': e1, '🦝': e1, '🦞': e1, '🦟': e1, '🦠': e1, '🦡': e1, '🦢': e1, '🦣': e1, '🦤': e1, '🦥': e1, '🦦': e1, '🦧': e1, '🦨': e1, '🦩': e1, '🦪': e1, '🦫': e1, '🦬': e1, '🦭': e1, '🦮': e1, '🦯': e1, '🦰': e203, '🦱': e203, '🦲': e203, '🦳': e203, '🦴': e1, '🦵': e22, '🦶': e22, '🦷': e1, '🦸': e109, '🦹': e109, '🦺': e1, '🦻': e22, '🦼': e1, '🦽': e1, '🦾': e1, '🦿': e1, '🧀': e1, '🧁': e1, '🧂': e1, '🧃': e1, '🧄': e1, '🧅': e1, '🧆': e1, '🧇': e1, '🧈': e1, '🧉': e1, '🧊': e1, '🧋': e1, '🧌': e1, '🧍': e109, '🧎': e197, '🧏': e109, '🧐': e1, '🧑': e40, '🧒': e22, '🧓': e22, '🧔': e109, '🧕': e22, '🧖': e109, '🧗': e109, '🧘': e109, '🧙': e109, '🧚': e109, '🧛': e109, '🧜': e109, '🧝': e109, '🧞': e110, '🧟': e110, '🧠': e1, '🧡': e1, '🧢': e1, '🧣': e1, '🧤': e1, '🧥': e1, '🧦': e1, '🧧': e1, '🧨': e1, '🧩': e1, '🧪': e1, '🧫': e1, '🧬': e1, '🧭': e1, '🧮': e1, '🧯': e1, '🧰': e1, '🧱': e1, '🧲': e1, '🧳': e1, '🧴': e1, '🧵': e1, '🧶': e1, '🧷': e1, '🧸': e1, '🧹': e1, '🧺': e1, '🧻': e1, '🧼': e1, '🧽': e1, '🧾': e1, '🧿': e1, '🩰': e1, '🩱': e1, '🩲': e1, '🩳': e1, '🩴': e1, '🩵': e1, '🩶': e1, '🩷': e1, '🩸': e1, '🩹': e1, '🩺': e1, '🩻': e1, '🩼': e1, '🪀': e1, '🪁': e1, '🪂': e1, '🪃': e1, '🪄': e1, '🪅': e1, '🪆': e1, '🪇': e1, '🪈': e1, '🪐': e1, '🪑': e1, '🪒': e1, '🪓': e1, '🪔': e1, '🪕': e1, '🪖': e1, '🪗': e1, '🪘': e1, '🪙': e1, '🪚': e1, '🪛': e1, '🪜': e1, '🪝': e1, '🪞': e1, '🪟': e1, '🪠': e1, '🪡': e1, '🪢': e1, '🪣': e1, '🪤': e1, '🪥': e1, '🪦': e1, '🪧': e1, '🪨': e1, '🪩': e1, '🪪': e1, '🪫': e1, '🪬': e1, '🪭': e1, '🪮': e1, '🪯': e1, '🪰': e1, '🪱': e1, '🪲': e1, '🪳': e1, '🪴': e1, '🪵': e1, '🪶': e1, '🪷': e1, '🪸': e1, '🪹': e1, '🪺': e1, '🪻': e1, '🪼': e1, '🪽': e1, '🪿': e1, '🫀': e1, '🫁': e1, '🫂': e1, '🫃': e22, '🫄': e22, '🫅': e22, '🫎': e1, '🫏': e1, '🫐': e1, '🫑': e1, '🫒': e1, '🫓': e1, '🫔': e1, '🫕': e1, '🫖': e1, '🫗': e1, '🫘': e1, '🫙': e1, '🫚': e1, '🫛': e1, '🫠': e1, '🫡': e1, '🫢': e1, '🫣': e1, '🫤': e1, '🫥': e1, '🫦': e1, '🫧': e1, '🫨': e1, '🫰': e22, '🫱': e24, '🫲': e22, '🫳': e22, '🫴': e22, '🫵': e22, '🫶': e22, '🫷': e22, '🫸': e22, }
