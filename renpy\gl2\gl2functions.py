# This file has been automatically generated by module/uguu/generate_required_functions.py,
# please do not edit it by hand.

from __future__ import division, absolute_import, with_statement, print_function, unicode_literals
from renpy.compat import PY2, basestring, bchr, bord, chr, open, pystr, range, round, str, tobytes, unicode # *



required_functions = [
    "glActiveTexture",
    "glAttachShader",
    "glBindFramebuffer",
    "glBindRenderbuffer",
    "glBindTexture",
    "glBlendEquation",
    "glBlendEquationSeparate",
    "glBlendFunc",
    "glBlendFuncSeparate",
    "glClear",
    "glClearColor",
    "glColorMask",
    "glCompileShader",
    "glCopyTexImage2D",
    "glCreateProgram",
    "glCreateShader",
    "glDeleteFramebuffers",
    "glDeleteRenderbuffers",
    "glDeleteShader",
    "glDeleteTextures",
    "glDisableVertexAttribArray",
    "glDrawElements",
    "glEnable",
    "glEnableVertexAttribArray",
    "glFinish",
    "glFramebufferRenderbuffer",
    "glFramebufferTexture2D",
    "glGenFramebuffers",
    "glGenRenderbuffers",
    "glGenTextures",
    "glGenerateMipmap",
    "glGetAttribLocation",
    "glGetFloatv",
    "glGetIntegerv",
    "glGetProgramInfoLog",
    "glGetProgramiv",
    "glGetShaderInfoLog",
    "glGetShaderiv",
    "glGetString",
    "glGetUniformLocation",
    "glHint",
    "glLinkProgram",
    "glPixelStorei",
    "glReadPixels",
    "glRenderbufferStorage",
    "glShaderSource",
    "glTexImage2D",
    "glTexParameterf",
    "glTexParameteri",
    "glUniform1f",
    "glUniform1i",
    "glUniform2f",
    "glUniform3f",
    "glUniform4f",
    "glUniformMatrix4fv",
    "glUseProgram",
    "glVertexAttribPointer",
    "glViewport",
]

