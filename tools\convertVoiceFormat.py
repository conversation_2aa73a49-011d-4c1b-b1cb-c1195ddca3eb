import os
import re
import sys

def process_rpy_file(file_path):
    # 检查文件是否为 .rpy 文件
    if not file_path.endswith('.rpy'):
        print(f"Skipping non-rpy file: {file_path}")
        return

    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # 正则表达式匹配 voice 行
        voice_pattern = re.compile(r"(voice\s+['\"][^'\"]+)\.mp3(['\"])")

        # 修改内容
        modified_lines = [
            voice_pattern.sub(r"\1\2", line) for line in lines
        ]

        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.writelines(modified_lines)

        print(f"Processed file: {file_path}")

    except Exception as e:
        print(f"An error occurred while processing {file_path}: {e}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python convertVoiceFormat.py <directory_path>")
        return

    directory_path = sys.argv[1]

    if not os.path.isdir(directory_path):
        print(f"Error: {directory_path} is not a valid directory.")
        return

    # 遍历目录，查找所有 .rpy 文件
    for root, _, files in os.walk(directory_path):
        for file in files:
            if file.endswith('.rpy'):
                file_path = os.path.join(root, file)
                process_rpy_file(file_path)

if __name__ == "__main__":
    main()