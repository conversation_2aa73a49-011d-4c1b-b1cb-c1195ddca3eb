import pandas as pd
import sys
import os
import re

script_dir = os.path.dirname(os.path.abspath(__file__))

def extract_translations(file_path):
    """
    从 Excel 文件中提取中英文键值对。
    :param file_path: Excel 文件路径
    :return: 包含中英文键值对的字典
    """
    # 加载 Excel 文件
    excel_data = pd.ExcelFile(file_path)

    translations = {}

    # 遍历每个子表
    for sheet_name in excel_data.sheet_names:
        # 读取子表数据
        sheet_data = excel_data.parse(sheet_name)
        
        # 确保子表有足够的列
        if sheet_data.shape[1] < 5:
            print(f"子表 '{sheet_name}' 列数不足，跳过...")
            continue
        
        # 遍历每一行，提取第 4 列和第 5 列的内容
        for index, row in sheet_data.iterrows():
            chinese_text = row.iloc[3]  # 第 4 列
            english_text = row.iloc[4]  # 第 5 列
            
            # 跳过中文或英文为空的行
            if pd.isna(chinese_text) or pd.isna(english_text):
                continue
            
            # 将键值对存储到字典中
            english_text = english_text.replace('\"', '\\"')
            english_text = english_text.replace("'", "\\'")
            translations[chinese_text] = english_text
    
    return translations

def check_dialogue_tab(translations):
    """
    解析 dialogue.tab 文件，将 Identifier 作为 key，其他列作为 value。
    value 的形式为：Character = xxx，Dialogue = xxx，Filename = xxx。
    """
    dialogue_data = {}

    # 获取 dialogue.tab 文件路径（上一级目录）
    dialogue_tab_path = os.path.join(script_dir, "..", "dialogue.tab")
    
    # 检查文件是否存在
    if not os.path.exists(dialogue_tab_path):
        print(f"文件 {dialogue_tab_path} 不存在！")
        return None

    try:
        # 打开文件并读取内容
        with open(dialogue_tab_path, mode='r', encoding='utf-8') as file:
            lines = file.readlines()

            # 跳过第一行（表头）
            for line in lines[1:]:
                # 按制表符分割每一行
                columns = line.strip().split('\t')
                
                # 确保列数足够
                if len(columns) < 6:
                    continue

                # 获取 Identifier 作为 key
                identifier = columns[0]
                if not identifier:
                    continue  # 如果 Identifier 为空，跳过该行

                # 构造 value组
                value = {
                    'Character' : columns[1].strip(),
                    'Dialogue' : columns[2].strip(),
                    'Filename' : columns[3].strip(),
                }

                # 如果有翻译，则添加到 value 中
                if value['Dialogue'] in translations:
                    value['english'] = translations[value['Dialogue']].strip()

                # 将 key-value 存入字典
                dialogue_data[identifier] = value
                # print(f"Identifier: {identifier}, Value: {value}")  # 打印每个 Identifier 和对应的 value

        return dialogue_data

    except FileNotFoundError:
        print(f"Error: File not found at {dialogue_tab_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

    return None

def update_translations(dialogue_data, translations):
    """
    在 game/tl/english 文件夹下检查所有 .rpy 文件，
    根据 dialogue_data 替换翻译文本。
    
    :param dialogue_data: 包含 Identifier 和对应翻译的字典
    """
    # 获取目标目录路径
    target_dir = os.path.join(script_dir, "..", "game", "tl", "english")
    
    # 检查目录是否存在
    if not os.path.exists(target_dir):
        print(f"目录 {target_dir} 不存在！")
        return

    # 遍历目标目录下的所有 .rpy 文件
    for root, _, files in os.walk(target_dir):
        for file in files:
            if file.endswith(".rpy"):
                file_path = os.path.join(root, file)
                update_file_translations(file_path, dialogue_data)
                update_words_translations(file_path, translations)


def update_file_translations(file_path, dialogue_data):
    """
    更新单个 .rpy 文件中的翻译文本。
    
    :param file_path: .rpy 文件路径
    :param dialogue_data: 包含 Identifier 和对应翻译的字典
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        updated_lines = []
        i = 0
        while i < len(lines):
            line = lines[i]

            # 匹配 translate 块的开头
            match = re.match(r'translate english (.+):', line)
            if match:
                identifier = match.group(1).strip()  # 提取 Identifier
                if identifier in dialogue_data and 'english' in dialogue_data[identifier]:
                    # 获取翻译文本
                    english_translation = dialogue_data[identifier]['english']

                    # 处理整个语句块
                    updated_lines.append(line)  # 保留 translate 行
                    i += 1

                    # 跳过空行和注释行，直到找到翻译文本行
                    while i < len(lines) and (lines[i].strip() == "" or lines[i].strip().startswith("#") or lines[i].strip().startswith("voice")):
                        updated_lines.append(lines[i])  # 保留空行和注释行
                        i += 1

                    # 检查是否是翻译文本行
                    if i < len(lines):
                        corrected_line = lines[i]
                        corrected_line = corrected_line.replace('\\"', '')
                        corrected_line = corrected_line.replace("\\'", '')
                        text_match = re.search(r'(["\'])(?:(?!\1).)*?\1$', corrected_line)
                        if text_match:
                            prefix = corrected_line[:text_match.start()]  # 引号前的内容
                            suffix = corrected_line[text_match.end():]   # 引号后的内容
                            updated_lines.append(f'{prefix}{text_match.group(1)}{english_translation}{text_match.group(1)}{suffix}')
                            i += 1
                        else:
                            updated_lines.append(lines[i])  # 如果没有匹配到翻译文本，保留原行
                            i += 1
                        continue

            # 如果不匹配 translate 块，保留原行
            updated_lines.append(line)
            i += 1

        # 将更新后的内容写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.writelines(updated_lines)

        print(f"已更新文件: {file_path}")

    except Exception as e:
        print(f"更新文件 {file_path} 时发生错误: {e}")

def update_words_translations(file_path, translations):
    """
    更新单个 .rpy 文件中的翻译文本。
    
    :param file_path: .rpy 文件路径
    :param translations: 包含中文和对应翻译的字典
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        updated_lines = []
        i= 0

        while i < len(lines):
            line = lines[i]

            # 匹配 translate english strings: 块的开头
            if line.strip().startswith("translate english strings:"):
                updated_lines.append(line)  # 保留 translate english strings 行
                i += 1

                # 处理 old/new 语句块
                while i < len(lines):
                    old_match = re.match(r'\s*old\s+"(.+)"', lines[i])
                    new_match = re.match(r'\s*new\s+"(.*)"', lines[i + 1] if i + 1 < len(lines) else "")

                    if old_match and new_match:
                        chinese_text = old_match.group(1).strip()
                        if chinese_text in translations:
                            english_translation = translations[chinese_text]
                            updated_lines.append(f'    old "{chinese_text}"\n')
                            updated_lines.append(f'    new "{english_translation}"\n')
                        else:
                            updated_lines.append(lines[i])  # 保留原行
                            updated_lines.append(lines[i + 1])  # 保留原行
                        i += 2
                    elif lines[i].strip() == "" or lines[i].strip().startswith("#"):
                        updated_lines.append(lines[i])  # 保留空行或注释行
                        i += 1
                    else:
                        break
            else:
                updated_lines.append(line)
                i += 1

        # 将更新后的内容写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.writelines(updated_lines)

        print(f"已更新文件: {file_path}")

    except Exception as e:
        print(f"更新文件 {file_path} 时发生错误: {e}")
    

def output_temp_file(data):
    output_file = os.path.join(script_dir, "temp.txt")
    with open(output_file, 'w', encoding='utf-8') as f:
        for k, v in data.items():
            f.write(f"{k} -> {v}\n")


if __name__ == "__main__":
    
    if len(sys.argv) != 2:
        print(f"wrong args: {sys.argv}")
        sys.exit(1)
    translations_path = sys.argv[1]
    
    try:
        # 提取中英文键值对
        translations = extract_translations(translations_path)
        
        # 打印结果
        output_file = os.path.join(script_dir, "mapping.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            for chinese, english in translations.items():
                f.write(f"{chinese} -> {english}\n")

        dialogue_data = check_dialogue_tab(translations)
        output_temp_file(dialogue_data)

        if dialogue_data:
            update_translations(dialogue_data, translations)

    except Exception as e:
        print(f"发生错误: {e}")